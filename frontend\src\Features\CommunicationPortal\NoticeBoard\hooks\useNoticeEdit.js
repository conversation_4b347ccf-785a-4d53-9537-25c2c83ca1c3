import { useSelector, useDispatch } from 'react-redux';
import { useCallback, useEffect } from 'react';
import {
  fetchNoticeById,
  updateNotice
} from '../../../../redux/slices/api/noticeApi';
import {
  clearErrors,
  clearSuccess,
  clearSelectedNotice,
  resetUpdateState,
  clearAllStates
} from '../../../../redux/slices/notices/noticeSlice';

/**
 * Custom hook for notice editing operations
 * Provides a clean interface for editing notices with Redux state management
 */
export const useNoticeEdit = () => {
  const dispatch = useDispatch();
  
  // Select relevant state from Redux store
  const {
    selectedNotice,
    updating,
    updateError,
    updateSuccess,
    message,
    loading,
    error
  } = useSelector((state) => state.notices);

  // Load a specific notice for editing
  const loadNotice = useCallback((id) => {
    return dispatch(fetchNoticeById(id));
  }, [dispatch]);

  // Update an existing notice
  const updateExistingNotice = useCallback(({ id, data }) => {
    return dispatch(updateNotice({ id, data }));
  }, [dispatch]);

  // Clear the selected notice
  const clearNotice = useCallback(() => {
    dispatch(clearSelectedNotice());
  }, [dispatch]);

  // Clear all errors
  const clearAllErrors = useCallback(() => {
    dispatch(clearErrors());
  }, [dispatch]);

  // Clear all success states
  const clearAllSuccess = useCallback(() => {
    dispatch(clearSuccess());
  }, [dispatch]);

  // Reset update state
  const resetUpdate = useCallback(() => {
    dispatch(resetUpdateState());
  }, [dispatch]);

  // Clear all states (comprehensive cleanup)
  const clearAllState = useCallback(() => {
    dispatch(clearAllStates());
  }, [dispatch]);

  // Computed values
  const computed = {
    // Check if any operation is in progress
    isLoading: loading || updating,
    
    // Check if there are any errors
    hasErrors: !!(error || updateError),
    
    // Check if there are any success states
    hasSuccess: !!updateSuccess,
    
    // Get current notice data
    currentNotice: selectedNotice,
    
    // Check if notice is loaded
    isNoticeLoaded: !!selectedNotice && !loading,
    
    // Check if update is in progress
    isUpdating: updating
  };

  // Helper functions
  const helpers = {
    // Check if notice exists and is loaded
    isNoticeReady: () => !!selectedNotice && !loading && !error,
    
    // Get notice by checking if it matches the current selected notice
    getCurrentNotice: () => selectedNotice,
    
    // Check if there are unsaved changes (would need to be implemented in component)
    hasUnsavedChanges: (originalData, currentData) => {
      if (!originalData || !currentData) return false;
      return JSON.stringify(originalData) !== JSON.stringify(currentData);
    },
    
    // Format error message for display
    getErrorMessage: () => {
      if (updateError) {
        if (typeof updateError === 'string') return updateError;
        if (updateError.message) return updateError.message;
        if (updateError.error) return updateError.error;
        return 'An error occurred while updating the notice';
      }
      if (error) {
        if (typeof error === 'string') return error;
        if (error.message) return error.message;
        return 'An error occurred while loading the notice';
      }
      return null;
    },
    
    // Get success message
    getSuccessMessage: () => {
      return message || 'Notice updated successfully';
    }
  };

  // Auto-cleanup effect
  useEffect(() => {
    // Cleanup function to run when component unmounts
    return () => {
      // Only clear if we're actually unmounting, not just re-rendering
      // This helps prevent unnecessary state clearing during normal operation
    };
  }, []);

  return {
    // State
    selectedNotice,
    loading,
    updating,
    updateError,
    updateSuccess,
    message,
    error,
    
    // Actions
    loadNotice,
    updateNotice: updateExistingNotice,
    clearNotice,
    clearAllErrors,
    clearAllSuccess,
    resetUpdate,
    clearAllState,
    
    // Computed values
    ...computed,
    
    // Helper functions
    ...helpers
  };
};

/**
 * Hook specifically for notice editing with automatic loading
 * @param {string|number} noticeId - ID of the notice to edit
 * @returns {Object} - Notice editing state and actions
 */
export const useNoticeEditWithId = (noticeId) => {
  const editHook = useNoticeEdit();
  const { loadNotice, clearNotice, resetUpdate } = editHook;

  // Load notice on mount or when ID changes
  useEffect(() => {
    if (noticeId) {
      loadNotice(noticeId);
    }
    
    // Cleanup on unmount or ID change
    return () => {
      clearNotice();
      resetUpdate();
    };
  }, [noticeId, loadNotice, clearNotice, resetUpdate]);

  return editHook;
};

export default useNoticeEdit;
