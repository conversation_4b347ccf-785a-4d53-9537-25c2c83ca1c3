/**
 * Notice Board Utility Functions
 * Provides utility functions for notice board operations including data formatting,
 * validation, and API data transformation
 */

/**
 * Format notice data for API submission
 * @param {Object} formData - Form data from the notice form
 * @returns {Object} - Formatted data for API
 */
export const formatNoticeForApi = (formData) => {
  if (!formData) return null;

  // Convert frontend post_as to backend format
  const convertPostAsToBackend = (frontendValue) => {
    const mapping = {
      'Creator': 'creator',
      'Group': 'group', 
      'Member': 'member'
    };
    return mapping[frontendValue] || 'creator';
  };

  // Handle post_as logic
  const post_as = convertPostAsToBackend(formData.postAs);
  let posted_group = null;
  let posted_member = null;

  if (post_as === 'group' && formData.selectedGroupId) {
    posted_group = parseInt(formData.selectedGroupId);
  } else if (post_as === 'member' && formData.selectedMemberId) {
    posted_member = parseInt(formData.selectedMemberId);
  }

  // Handle priority
  const priority = formData.priority?.toLowerCase() || 'normal';

  // Handle label
  const label = formData.label || '';

  // Handle tower and unit IDs
  const target_tower_ids = Array.isArray(formData.selectedTowers) 
    ? formData.selectedTowers.filter(id => id !== 'All' && id !== '').map(id => parseInt(id))
    : [];

  const target_unit_ids = Array.isArray(formData.selectedUnits)
    ? formData.selectedUnits.filter(id => id !== 'All' && id !== '').map(id => parseInt(id))
    : [];

  // Handle base64 attachments (for new files)
  const base64_attachments = [];
  if (formData.attachments && Array.isArray(formData.attachments)) {
    formData.attachments.forEach(attachment => {
      if (attachment.base64 && !attachment.isExisting) {
        base64_attachments.push({
          name: attachment.name,
          data: attachment.base64,
          type: attachment.type
        });
      }
    });
  }

  // Prepare the base notice data
  const noticeData = {
    title: formData.title.trim(),
    description: formData.description ? formData.description.trim() : '',  // Make description optional
    post_as: post_as,
    posted_group: posted_group,
    posted_member: posted_member,
    priority: priority,
    label: label.trim(),
    start_date: formData.startDate,
    start_time: formData.startTime,
    end_date: formData.endDate,
    end_time: formData.endTime,
    target_tower_ids: target_tower_ids,
    target_unit_ids: target_unit_ids,
    base64_attachments: base64_attachments
  };

  // Add attachments to delete if provided (for edit operations)
  if (formData.attachmentsToDelete && formData.attachmentsToDelete.length > 0) {
    noticeData.attachments_to_delete = formData.attachmentsToDelete;
  }

  return noticeData;
};

/**
 * Format notice data for editing
 * @param {Object} notice - Notice object from API
 * @returns {Object} - Formatted data for form
 */
export const formatNoticeForEdit = (notice) => {
  if (!notice) return null;

  // Convert backend post_as string to frontend format (capitalized for Edit Notice component)
  const convertPostAsToFrontend = (backendValue) => {
    const mapping = {
      'creator': 'Creator',
      'group': 'Group',
      'member': 'Member'
    };
    return mapping[backendValue] || 'Creator';
  };

  // Format dates for form inputs (YYYY-MM-DD format)
  const formatDateForInput = (dateString) => {
    if (!dateString) return '';
    
    try {
      // Handle different date formats
      let date;
      if (dateString.includes('T')) {
        // ISO format: 2024-01-15T00:00:00Z
        date = new Date(dateString);
      } else if (dateString.includes('-') && dateString.length === 10) {
        // Already in YYYY-MM-DD format
        return dateString;
      } else {
        // Try to parse as is
        date = new Date(dateString);
      }
      
      if (isNaN(date.getTime())) {
        console.warn('Invalid date:', dateString);
        return '';
      }
      
      // Format as YYYY-MM-DD
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  // Format time for form inputs (HH:MM format)
  const formatTimeForInput = (timeString) => {
    if (!timeString) return '';
    
    try {
      // If time includes seconds, remove them
      if (timeString.includes(':') && timeString.split(':').length === 3) {
        const [hours, minutes] = timeString.split(':');
        return `${hours}:${minutes}`;
      }
      
      // If time is already in HH:MM format, return as is
      if (timeString.includes(':') && timeString.split(':').length === 2) {
        return timeString;
      }
      
      // Try to parse as Date and extract time
      const date = new Date(`2000-01-01T${timeString}`);
      if (!isNaN(date.getTime())) {
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${hours}:${minutes}`;
      }
      
      return timeString;
    } catch (error) {
      console.error('Error formatting time:', error);
      return timeString;
    }
  };

  return {
    title: notice.title || '',
    description: notice.description || '',
    postAs: convertPostAsToFrontend(notice.post_as),
    priority: notice.priority || 'normal',
    label: notice.label || '',
    startDate: formatDateForInput(notice.start_date),
    startTime: formatTimeForInput(notice.start_time),
    endDate: formatDateForInput(notice.end_date),
    endTime: formatTimeForInput(notice.end_time)
  };
};

/**
 * Validate notice data before submission
 * @param {Object} data - Notice data to validate
 * @returns {Object} - Validation result with isValid and errors
 */
export const validateNoticeData = (data) => {
  const errors = {};
  let isValid = true;

  // Required fields validation
  if (!data.title?.trim()) {
    errors.title = 'Title is required';
    isValid = false;
  }

  if (!data.priority) {
    errors.priority = 'Priority is required';
    isValid = false;
  }

  if (!data.label?.trim()) {
    errors.label = 'Label is required';
    isValid = false;
  }

  if (!data.startDate) {
    errors.startDate = 'Start date is required';
    isValid = false;
  }

  if (!data.startTime) {
    errors.startTime = 'Start time is required';
    isValid = false;
  }

  if (!data.endDate) {
    errors.endDate = 'End date is required';
    isValid = false;
  }

  if (!data.endTime) {
    errors.endTime = 'End time is required';
    isValid = false;
  }

  // Date/time validation
  if (data.startDate && data.startTime && data.endDate && data.endTime) {
    const startDateTime = new Date(`${data.startDate}T${data.startTime}`);
    const endDateTime = new Date(`${data.endDate}T${data.endTime}`);
    
    if (startDateTime >= endDateTime) {
      errors.dateTime = 'End date/time must be after start date/time';
      isValid = false;
    }
  }

  // Post as validation
  if (data.postAs === 'Group' && !data.selectedGroupId) {
    errors.selectedGroupId = 'Please select a group';
    isValid = false;
  }

  if (data.postAs === 'Member' && !data.selectedMemberId) {
    errors.selectedMemberId = 'Please select a member';
    isValid = false;
  }

  return { isValid, errors };
};

/**
 * Filter notices by status
 * @param {Array} notices - Array of notices
 * @param {string} status - Status to filter by
 * @returns {Array} - Filtered notices
 */
export const filterNoticesByStatus = (notices, status) => {
  if (!Array.isArray(notices)) return [];
  
  return notices.filter(notice => {
    if (!notice) return false;
    return notice.status === status;
  });
};

/**
 * Get notices by priority
 * @param {Array} notices - Array of notices
 * @param {string} priority - Priority to filter by
 * @returns {Array} - Filtered notices
 */
export const getNoticesByPriority = (notices, priority) => {
  if (!Array.isArray(notices)) return [];
  
  return notices.filter(notice => {
    if (!notice) return false;
    return notice.priority === priority;
  });
};

/**
 * Format date for display
 * @param {string} dateString - Date string
 * @param {string} timeString - Time string (optional)
 * @returns {string} - Formatted date string
 */
export const formatDateForDisplay = (dateString, timeString) => {
  if (!dateString) return '';

  try {
    const date = new Date(`${dateString}${timeString ? ` ${timeString}` : ''}`);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      ...(timeString && {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      })
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};

/**
 * Check if a file is a valid image for notice board
 * @param {File} file - File to check
 * @returns {boolean} - True if valid image
 */
export const isValidNoticeImage = (file) => {
  if (!file) return false;

  // Notice board only allows images
  const validImageTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/bmp',
    'image/tiff',
    'image/webp',
    'image/heic',
    'image/heif',
    'image/svg+xml',
    'image/x-eps',
    'image/vnd.adobe.photoshop', // PSD
    'image/x-exr',
    'image/vnd.ms-dds',
    'image/x-icon',
    'image/apng'
  ];

  return validImageTypes.includes(file.type) ||
         /\.(jpe?g|png|gif|bmp|tiff?|webp|heic|heif|svg|eps|psd|exr|dds|ico|apng)$/i.test(file.name);
};

/**
 * Get file size limit for notice board attachments
 * @returns {number} - Size limit in bytes (5MB)
 */
export const getNoticeFileSizeLimit = () => {
  return 5 * 1024 * 1024; // 5MB
};

/**
 * Validate notice board file upload
 * @param {File} file - File to validate
 * @returns {Object} - Validation result with isValid and error message
 */
export const validateNoticeFile = (file) => {
  if (!file) {
    return { isValid: false, error: 'No file provided' };
  }

  // Check if it's a valid image
  if (!isValidNoticeImage(file)) {
    return {
      isValid: false,
      error: 'Only image files are allowed for notice board. PDF and other file types are not permitted.'
    };
  }

  // Check file size
  const sizeLimit = getNoticeFileSizeLimit();
  if (file.size > sizeLimit) {
    return {
      isValid: false,
      error: `File size exceeds 5MB limit. Current size: ${(file.size / (1024 * 1024)).toFixed(2)}MB`
    };
  }

  return { isValid: true, error: null };
};

/**
 * Convert file to base64
 * @param {File} file - File to convert
 * @returns {Promise<string>} - Base64 string
 */
export const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
};

/**
 * Convert base64 to blob
 * @param {string} base64 - Base64 string
 * @param {string} mimeType - MIME type
 * @returns {Blob} - Blob object
 */
export const base64ToBlob = (base64, mimeType) => {
  const byteCharacters = atob(base64.split(',')[1]);
  const byteNumbers = new Array(byteCharacters.length);

  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }

  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mimeType });
};

export default {
  formatNoticeForApi,
  formatNoticeForEdit,
  validateNoticeData,
  filterNoticesByStatus,
  getNoticesByPriority,
  formatDateForDisplay,
  isValidNoticeImage,
  getNoticeFileSizeLimit,
  validateNoticeFile,
  fileToBase64,
  base64ToBlob
};
